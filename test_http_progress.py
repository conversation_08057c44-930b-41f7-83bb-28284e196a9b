#!/usr/bin/env python3
"""
Test script to make actual HTTP requests to Django API to trigger progress display.
This will test if progress appears in Django runserver stdout when called via HTTP.
"""

import requests
import json
import time
from requests.auth import HTTPBasicAuth

def test_http_progress():
    """Test progress display via HTTP requests to Django API."""
    
    print("Testing HTTP API progress display...")
    print("Make sure Django server is running on port 8002")
    print("Make sure MCP server is running on port 9000")
    print()

    base_url = "http://127.0.0.1:8002"
    
    # Try to access the API without authentication first to see what happens
    print("1. Testing API access...")
    
    try:
        # Test if we can access the API
        response = requests.get(f"{base_url}/gaia_chat/api/llm/providers/")
        print(f"API access response: {response.status_code}")
        
        if response.status_code == 302:
            print("API requires authentication (redirecting to login)")
            print("Using Django's test client to bypass authentication...")
            return test_with_django_client()
            
        elif response.status_code == 200:
            print("API accessible without authentication!")
            return test_api_directly(base_url)
        else:
            print(f"Unexpected response: {response.status_code}")
            print(response.text[:200])
            
    except Exception as e:
        print(f"Error accessing API: {e}")
        return False

def test_with_django_client():
    """Test using Django's test client to bypass authentication."""
    
    print("\nTesting with Django test client...")
    
    try:
        import os
        import sys
        import django
        
        # Set up Django environment
        sys.path.insert(0, '/home/<USER>/django-projects/agbase_admin')
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangaia.settings')
        django.setup()
        
        from django.test import Client
        from django.contrib.auth.models import User
        
        # Create a test client
        client = Client()
        
        # Create a test user
        try:
            user = User.objects.create_user(
                username='testuser',
                email='<EMAIL>',
                password='testpass123'
            )
            print("Created test user")
        except:
            # User might already exist
            user = User.objects.get(username='testuser')
            print("Using existing test user")
        
        # Login the test user
        client.force_login(user)
        print("Logged in test user")
        
        # Test setting LLM to MCP HTTP
        print("2. Setting LLM to MCP HTTP...")
        response = client.post('/gaia_chat/api/llm/set/', 
                              json.dumps({
                                  "provider": "mcp-http",
                                  "model": "http://0.0.0.0:9000/mcp"
                              }),
                              content_type='application/json')
        print(f"Set LLM response: {response.status_code}")
        
        if response.status_code == 200:
            print("LLM set successfully")
            
            # Create a conversation
            print("3. Creating conversation...")
            response = client.post('/gaia_chat/api/conversations/create/',
                                  json.dumps({
                                      "title": "Test HTTP Progress"
                                  }),
                                  content_type='application/json')
            print(f"Create conversation response: {response.status_code}")
            
            if response.status_code == 200:
                conv_data = response.json()
                conversation_id = conv_data["conversation"]["id"]
                print(f"Created conversation: {conversation_id}")
                
                # Send long_task message
                print("4. Sending long_task message...")
                print("Check Django runserver stdout for progress!")
                print("=" * 60)
                
                response = client.post('/gaia_chat/api/messages/send-stream/',
                                      json.dumps({
                                          "message": "long_task",
                                          "conversation_id": conversation_id
                                      }),
                                      content_type='application/json')
                
                print(f"Send message response: {response.status_code}")
                
                if response.status_code == 200:
                    print("Message sent successfully!")
                    print("Check the Django runserver terminal for progress bars!")
                    return True
                else:
                    print(f"Error sending message: {response.content}")
            else:
                print(f"Error creating conversation: {response.content}")
        else:
            print(f"Error setting LLM: {response.content}")
            
    except Exception as e:
        print(f"Error with Django client: {e}")
        import traceback
        traceback.print_exc()
        
    return False

def test_api_directly(base_url):
    """Test API directly if no authentication required."""
    print("Testing API directly...")

    try:
        # Set LLM to MCP HTTP
        print("2. Setting LLM to MCP HTTP...")
        response = requests.post(f"{base_url}/gaia_chat/api/llm/set/",
                               json={
                                   "provider": "mcp-http",
                                   "model": "http://0.0.0.0:9000/mcp"
                               })
        print(f"Set LLM response: {response.status_code}")

        if response.status_code == 200:
            print("LLM set successfully")

            # Create a conversation
            print("3. Creating conversation...")
            response = requests.post(f"{base_url}/gaia_chat/api/conversations/create/",
                                   json={
                                       "title": "Test HTTP Progress"
                                   })
            print(f"Create conversation response: {response.status_code}")

            if response.status_code == 200:
                try:
                    conv_data = response.json()
                    conversation_id = conv_data["conversation"]["id"]
                    print(f"Created conversation: {conversation_id}")
                except:
                    print(f"Response is not JSON. Content: {response.text[:200]}...")
                    return False

                # Send long_task message
                print("4. Sending long_task message...")
                print("Check Django runserver stdout for progress!")
                print("=" * 60)

                response = requests.post(f"{base_url}/gaia_chat/api/messages/send-stream/",
                                       json={
                                           "message": "long_task",
                                           "conversation_id": conversation_id
                                       },
                                       stream=True)

                print(f"Send message response: {response.status_code}")

                if response.status_code == 200:
                    print("Streaming response:")
                    for line in response.iter_lines(decode_unicode=True):
                        if line:
                            print(f"Stream: {line}")
                    print("=" * 60)
                    print("✅ HTTP test completed!")
                    print("Check the Django runserver terminal for progress bars!")
                    return True
                else:
                    print(f"Error sending message: {response.text}")
            else:
                print(f"Error creating conversation: {response.text}")
        else:
            print(f"Error setting LLM: {response.text}")

    except Exception as e:
        print(f"Error with direct API: {e}")
        import traceback
        traceback.print_exc()

    return False

if __name__ == "__main__":
    test_http_progress()
