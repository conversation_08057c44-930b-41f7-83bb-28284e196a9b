#!/usr/bin/env python3
"""
Simple test to verify that progress updates appear in Django runserver stdout
when the long_task tool is called via HTTP API.
"""

import requests
import time

def test_django_progress():
    """Test that progress appears in Django runserver stdout."""
    
    print("Testing Django API progress display...")
    print("Make sure Django server is running on port 8002")
    print("Make sure MCP server is running on port 9000")
    print()
    
    # Test the direct tool call function
    base_url = "http://127.0.0.1:8002"
    
    try:
        # Import the function directly to test it
        import sys
        import os
        sys.path.insert(0, '/home/<USER>/django-projects/agbase_admin')
        
        from gaia.djangaia.gaia_chat.api import handle_direct_tool_call_with_progress, ProgressTracker
        
        print("Testing handle_direct_tool_call_with_progress function...")
        print("This should print progress to stdout:")
        print("=" * 60)
        
        # Create a tracker
        tracker = ProgressTracker()
        
        # Call the function
        import asyncio
        
        async def run_test():
            events = []
            async for event in handle_direct_tool_call_with_progress(
                "long_task", 
                "http://localhost:9000/mcp", 
                tracker
            ):
                events.append(event)
                print(f"Event received: {event[:100]}...")
                
                # Stop after a few events to avoid too much output
                if len(events) >= 5:
                    break
            
            print(f"\nCollected {len(events)} events")
            print(f"Progress count: {tracker.progress_count}")
            print(f"Info count: {tracker.info_count}")
            
        # Run the test
        asyncio.run(run_test())
        
        print("=" * 60)
        print("✅ Test completed!")
        print()
        print("If you see progress bars above (📊), then the implementation is working!")
        print("The same progress should appear in Django runserver stdout when")
        print("the API is called via HTTP requests.")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_django_progress()
