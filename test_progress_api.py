#!/usr/bin/env python3
"""
Test script to call the Django API and trigger the long_task tool
to verify that progress updates are printed to stdout.
"""

import requests
import json
import time

def test_long_task_progress():
    """Test the long_task tool call via the Django API."""
    
    # API endpoint
    base_url = "http://127.0.0.1:8002"
    
    # First, we need to set up the LLM to use MCP HTTP
    set_llm_url = f"{base_url}/gaia_chat/api/llm/set/"
    set_llm_data = {
        "provider": "mcp-http",
        "model": "http://0.0.0.0:9000/mcp"
    }
    
    print("Setting LLM to MCP HTTP...")
    try:
        response = requests.post(set_llm_url, json=set_llm_data)
        print(f"Set LLM response: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"Error setting LLM: {e}")
        return
    
    # Create a conversation
    create_conv_url = f"{base_url}/gaia_chat/api/conversations/create/"
    create_conv_data = {
        "title": "Test Long Task Progress"
    }
    
    print("Creating conversation...")
    try:
        response = requests.post(create_conv_url, json=create_conv_data)
        print(f"Create conversation response: {response.status_code} - {response.text}")
        
        if response.status_code == 200:
            conv_data = response.json()
            conversation_id = conv_data["conversation"]["id"]
            print(f"Created conversation: {conversation_id}")
        else:
            print("Failed to create conversation")
            return
    except Exception as e:
        print(f"Error creating conversation: {e}")
        return
    
    # Send a message to trigger long_task
    send_message_url = f"{base_url}/gaia_chat/api/messages/send-stream/"
    send_message_data = {
        "message": "long_task",
        "conversation_id": conversation_id
    }
    
    print("Sending long_task message...")
    print("Check the Django server stdout for progress updates!")
    print("=" * 60)
    
    try:
        # Use streaming to see the progress in real-time
        response = requests.post(
            send_message_url, 
            json=send_message_data,
            stream=True,
            headers={'Accept': 'text/event-stream'}
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("Streaming response from API:")
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    print(f"API: {line}")
        else:
            print(f"Error response: {response.text}")
            
    except Exception as e:
        print(f"Error sending message: {e}")

if __name__ == "__main__":
    print("Testing long_task progress display...")
    print("Make sure the Django server is running on port 8002")
    print("Make sure the MCP server is running on port 9000")
    print()
    
    test_long_task_progress()
