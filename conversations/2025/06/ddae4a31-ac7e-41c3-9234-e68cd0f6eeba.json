{"conversation_id": "ddae4a31-ac7e-41c3-9234-e68cd0f6eeba", "user_id": "sam", "title": "Conversation 2025-06-09 09:58:21", "metadata": {}, "messages": [{"role": "system", "content": "How can I help you today?", "timestamp": "2025-06-09T09:58:21.478574"}, {"role": "system", "content": "CONTEXT LOADED: /home/<USER>/django-projects/agbase_admin/gaia/gaia_ceto/example_contexts/companies_example.json\nType: companies\nTitle: Company Information\nDescription: Information about various companies that can be used as context in your chat.\n\nData (3 items):\n  1. Name: Acme Corporation, industry: Manufacturing, founded: 1942, employees: 5000, revenue: $500M, description: A global leader in manufacturing innovative products.\n  2. Name: TechNova, industry: Technology, founded: 2010, employees: 250, revenue: $75M, description: Cutting-edge software solutions for enterprise clients.\n  3. Name: GreenEarth, industry: Renewable Energy, founded: 2015, employees: 120, revenue: $30M, description: Sustainable energy solutions for a better future.\n\nThis contextual information is now available for reference in our conversation.", "timestamp": "2025-06-09T09:58:34.335920"}], "created_at": "2025-06-09T09:58:21.478497", "updated_at": "2025-06-09T09:58:34.335920"}