# Generated by Django 3.2.10 on 2025-06-17 22:07

from django.db import migrations, models
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Conversation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=255)),
                ('messages', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['id'], name='gaia_chat_c_id_fa5ca1_idx'),
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['created_at'], name='gaia_chat_c_created_48f2bd_idx'),
        ),
    ]
