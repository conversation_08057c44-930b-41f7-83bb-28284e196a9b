import uuid
from django.db import models
from django.utils import timezone


class Conversation(models.Model):
    """
    Model to store chat conversations.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255)
    messages = models.JSONField(default=list)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return f"{self.title} ({self.id})"

    class Meta:
        # Add indexes for better performance
        indexes = [
            models.Index(fields=['id']),
            models.Index(fields=['created_at']),
        ]
