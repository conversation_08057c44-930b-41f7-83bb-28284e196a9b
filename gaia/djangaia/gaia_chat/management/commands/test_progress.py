"""
Django management command to test progress display functionality.
This bypasses HTTP authentication and directly tests the progress handlers.

Usage:
    python manage.py test_progress
"""

import asyncio
from django.core.management.base import BaseCommand
from gaia_chat.api import handle_direct_tool_call_with_progress, ProgressTracker


class Command(BaseCommand):
    help = 'Test progress display functionality for MCP tool calls'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tool',
            type=str,
            default='long_task',
            help='Tool name to test (default: long_task)'
        )
        parser.add_argument(
            '--server',
            type=str,
            default='http://localhost:9000/mcp',
            help='MCP server URL (default: http://localhost:9000/mcp)'
        )

    def handle(self, *args, **options):
        tool_name = options['tool']
        server_url = options['server']
        
        self.stdout.write(
            self.style.SUCCESS(f'Testing progress display for tool: {tool_name}')
        )
        self.stdout.write(f'Server URL: {server_url}')
        self.stdout.write('=' * 60)
        
        # Run the async test
        asyncio.run(self.run_progress_test(tool_name, server_url))

    async def run_progress_test(self, tool_name, server_url):
        """Run the progress test asynchronously."""
        
        tracker = ProgressTracker()
        
        self.stdout.write('Starting progress test...')
        self.stdout.write('Progress should appear below:')
        self.stdout.write('-' * 40)
        
        try:
            events = []
            async for event in handle_direct_tool_call_with_progress(tool_name, server_url, tracker):
                events.append(event)
                # Don't print the events here - let the progress handlers print to stdout
                
                # Stop after collecting some events
                if len(events) >= 10:
                    break
            
            self.stdout.write('-' * 40)
            self.stdout.write(
                self.style.SUCCESS(f'✅ Test completed successfully!')
            )
            self.stdout.write(f'Collected {len(events)} events')
            self.stdout.write(f'Progress messages: {tracker.progress_count}')
            self.stdout.write(f'Info messages: {tracker.info_count}')
            
            if tracker.progress_count > 0:
                self.stdout.write(
                    self.style.SUCCESS('✅ Progress display is working correctly!')
                )
                self.stdout.write('The progress bars above should match the format from chat_term.py')
            else:
                self.stdout.write(
                    self.style.WARNING('⚠️ No progress messages received. Check MCP server.')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Test failed: {e}')
            )
            import traceback
            self.stdout.write(traceback.format_exc())
