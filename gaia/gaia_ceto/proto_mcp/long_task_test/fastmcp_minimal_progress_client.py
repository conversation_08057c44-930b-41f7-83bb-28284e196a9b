# FastMCP High-Level Progress Client
import asyncio
from fastmcp import Client
from fastmcp.client.logging import LogMessage

PROTOCOL = 'http'  # http or sse

# Global counters for statistics
progress_count = 0
info_count = 0


# ────────────────────────────────
# Progress updates (ctx.report_progress)
# ────────────────────────────────
async def cli_progress(progress: float,
                       total: float | None,
                       message: str | None) -> None:
    """Display progress with visual progress bar."""
    global progress_count
    progress_count += 1

    if total:
        pct = progress / total * 100

        # Create visual progress bar
        bar_length = 28
        filled = int(bar_length * pct / 100)
        bar = "█" * filled + "░" * (bar_length - filled)

        # Format message
        msg_text = f" – {message}" if message else ""

        print(f"📊 [{bar}] {pct:5.1f}% {int(progress)}/{int(total)}{msg_text}", flush=True)
    else:
        print(f"📊 Step {progress} – {message or ''}", flush=True)


# ────────────────────────────────
# Log messages (ctx.info / ctx.debug / …)
# ────────────────────────────────
async def cli_log(message: LogMessage) -> None:
    """
    Handle server-side logging with minimal display.

    Only count info messages, don't display them to keep output clean.
    """
    global info_count

    level = message.level.upper()

    # Count info messages but don't display them for cleaner output
    if level == "INFO":
        info_count += 1

    # Only display non-info messages (errors, warnings, etc.)
    if level != "INFO":
        logger = message.logger or "server"
        payload = message.data
        emoji = {"ERROR": "❌", "WARNING": "⚠️", "DEBUG": "🔍"}.get(level, "ℹ️")
        print(f"{emoji} [{level}] {logger}: {payload}", flush=True)


# ────────────────────────────────
# Main programme
# ────────────────────────────────
if PROTOCOL == 'http':
    url = "http://localhost:9000/mcp"
else:
    url = "http://localhost:9000/sse"


async def main() -> None:
    global progress_count, info_count

    # Reset counters
    progress_count = 0
    info_count = 0

    print("🚀 FastMCP High‑Level Progress Client")

    client = Client(
        url,
        progress_handler=cli_progress,
        log_handler=cli_log,
    )

    async with client:
        print("✅ Connected (high‑level client, token auto‑injected)")

        # Call long_task - progress and info will be handled by callbacks
        result = await client.call_tool("long_task")

    # Final summary
    print()
    print("🎉 Tool finished successfully")
    print(f"📊 Progress msgs: {progress_count}   ℹ️ Info msgs: {info_count}")


if __name__ == "__main__":
    asyncio.run(main())
