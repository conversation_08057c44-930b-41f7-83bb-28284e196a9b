#!/usr/bin/env python3
"""
Convert JSONL format agsearch data to companies JSON format for Django view.

This script reads the agsearch_random_10k.json file (which is in JSONL format)
and converts it to a proper JSON structure that matches the expected format
for the companies chat view.
"""

import json
import os
from typing import Dict, List, Any


def extract_company_info(agsearch_record: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract relevant company information from an agsearch record.
    
    Args:
        agsearch_record: Raw agsearch JSON record
        
    Returns:
        Cleaned company record in expected format
    """
    # Extract basic company info
    company_info = {
        "id": agsearch_record.get("index", 0),
        "name": agsearch_record.get("company_name", "Unknown Company"),
        "industry": extract_industry(agsearch_record),
        "founded": extract_founded_year(agsearch_record),
        "employees": extract_employee_count(agsearch_record),
        "revenue": extract_revenue(agsearch_record),
        "description": agsearch_record.get("short_description", "No description available."),
        "country": agsearch_record.get("country_code", "Unknown"),
        "region": agsearch_record.get("region", "Unknown"),
        "city": agsearch_record.get("city", "Unknown"),
        "website": agsearch_record.get("homepage_url", ""),
        "status": agsearch_record.get("status", "unknown"),
        "categories": agsearch_record.get("category_list", ""),
        "uuid": agsearch_record.get("uuid", ""),
        "crunchbase_url": agsearch_record.get("cb_url", "")
    }
    
    return company_info


def extract_industry(record: Dict[str, Any]) -> str:
    """Extract and clean industry information."""
    categories = record.get("category_list", "")
    if categories:
        # Take the first category as primary industry
        primary_category = categories.split(",")[0].strip()
        return primary_category
    return "Unknown"


def extract_founded_year(record: Dict[str, Any]) -> int:
    """Extract founded year from founded_on field."""
    founded_on = record.get("founded_on", "")
    if founded_on:
        try:
            # Extract year from ISO date format like "2021-01-01T00:00:00"
            year = int(founded_on.split("-")[0])
            return year
        except (ValueError, IndexError):
            pass
    return None


def extract_employee_count(record: Dict[str, Any]) -> str:
    """Extract and format employee count."""
    employee_count = record.get("employee_count", "")
    if employee_count:
        return employee_count
    return "Unknown"


def extract_revenue(record: Dict[str, Any]) -> str:
    """Extract revenue information (placeholder since not in agsearch data)."""
    # Agsearch data doesn't have revenue, so we'll generate placeholder
    # based on employee count or other factors
    employee_count = record.get("employee_count", "")
    
    if "1-10" in employee_count:
        return "< $1M"
    elif "11-50" in employee_count:
        return "$1M - $10M"
    elif "51-200" in employee_count:
        return "$10M - $50M"
    elif "201-500" in employee_count:
        return "$50M - $100M"
    elif "501-1000" in employee_count:
        return "$100M - $500M"
    elif "1001-5000" in employee_count:
        return "$500M - $1B"
    elif "5001-10000" in employee_count:
        return "$1B - $5B"
    elif "10000+" in employee_count:
        return "$5B+"
    else:
        return "Unknown"


def convert_jsonl_to_companies(input_file: str, output_file: str, max_companies: int = 1000):
    """
    Convert JSONL agsearch data to companies JSON format.
    
    Args:
        input_file: Path to input JSONL file
        output_file: Path to output JSON file
        max_companies: Maximum number of companies to include
    """
    companies = []
    
    print(f"Reading {input_file}...")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            if line_num > max_companies:
                break
                
            line = line.strip()
            if not line:
                continue
                
            try:
                # Parse each line as a separate JSON object
                agsearch_record = json.loads(line)
                
                # Extract company information
                company_info = extract_company_info(agsearch_record)
                companies.append(company_info)
                
                if line_num % 100 == 0:
                    print(f"Processed {line_num} companies...")
                    
            except json.JSONDecodeError as e:
                print(f"Error parsing line {line_num}: {e}")
                continue
            except Exception as e:
                print(f"Error processing line {line_num}: {e}")
                continue
    
    # Create the final JSON structure
    output_data = {
        "type": "companies",
        "title": f"Real Company Data from AgSearch ({len(companies)} companies)",
        "description": f"Real company data extracted from AgSearch database, converted from JSONL format. Contains {len(companies)} companies with detailed information including industry, location, employee count, and descriptions.",
        "data": companies
    }
    
    print(f"Writing {len(companies)} companies to {output_file}...")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    print(f"Successfully converted {len(companies)} companies!")
    print(f"Output file: {output_file}")


def main():
    """Main function to run the conversion."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    input_file = os.path.join(script_dir, "agsearch_random_10k.json")
    output_file = os.path.join(script_dir, "agsearch_companies_5000.json")
    
    if not os.path.exists(input_file):
        print(f"Error: Input file not found: {input_file}")
        return
    
    # Convert first 5000 companies (you can adjust this number)
    convert_jsonl_to_companies(input_file, output_file, max_companies=5000)


if __name__ == "__main__":
    main()
