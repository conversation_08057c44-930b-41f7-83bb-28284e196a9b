#!/usr/bin/env python3
"""
Enhanced OpenSearch to JSON converter with data transformation.

This script:
1. Queries OpenSearch for random company data
2. Transforms the raw data into Django-compatible format
3. Creates both JSONL (raw) and JSON (transformed) output files
4. Supports different company counts (1K, 10K, etc.)
"""

import json
import random
import os
from typing import Dict, List, Any
from opensearchpy import OpenSearch, helpers

# OpenSearch configuration
INDEX = "idx_agsearch_worker_giant1__2025-05-06-000155"
HOSTS = [{"host": "localhost", "port": 9290}]

# ➡️ bump timeout + enable retry on timeout
client = OpenSearch(
    hosts=HOSTS,
    timeout=60,                   # seconds (pick what you need)
    max_retries=3,
    retry_on_timeout=True,
    # http_auth=("user","pass"),  # add if your cluster is secured
)


def extract_company_info(agsearch_record: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract relevant company information from an agsearch record.

    Args:
        agsearch_record: Raw agsearch JSON record

    Returns:
        Cleaned company record in expected format
    """
    # Extract basic company info
    company_info = {
        "id": agsearch_record.get("index", 0),
        "name": agsearch_record.get("company_name", "Unknown Company"),
        "industry": extract_industry(agsearch_record),
        "founded": extract_founded_year(agsearch_record),
        "employees": extract_employee_count(agsearch_record),
        "revenue": extract_revenue(agsearch_record),
        "description": agsearch_record.get("short_description", "No description available."),
        "country": agsearch_record.get("country_code", "Unknown"),
        "region": agsearch_record.get("region", "Unknown"),
        "city": agsearch_record.get("city", "Unknown"),
        "website": agsearch_record.get("homepage_url", ""),
        "status": agsearch_record.get("status", "unknown"),
        "categories": agsearch_record.get("category_list", ""),
        "uuid": agsearch_record.get("uuid", ""),
        "crunchbase_url": agsearch_record.get("cb_url", "")
    }

    return company_info


def extract_industry(record: Dict[str, Any]) -> str:
    """Extract and clean industry information."""
    categories = record.get("category_list", "")
    if categories:
        # Take the first category as primary industry
        primary_category = categories.split(",")[0].strip()
        return primary_category
    return "Unknown"


def extract_founded_year(record: Dict[str, Any]) -> int:
    """Extract founded year from founded_on field."""
    founded_on = record.get("founded_on", "")
    if founded_on:
        try:
            # Extract year from ISO date format like "2021-01-01T00:00:00"
            year = int(founded_on.split("-")[0])
            return year
        except (ValueError, IndexError):
            pass
    return None


def extract_employee_count(record: Dict[str, Any]) -> str:
    """Extract and format employee count."""
    employee_count = record.get("employee_count", "")
    if employee_count:
        return employee_count
    return "Unknown"


def extract_revenue(record: Dict[str, Any]) -> str:
    """Extract revenue information (placeholder since not in agsearch data)."""
    # Agsearch data doesn't have revenue, so we'll generate placeholder
    # based on employee count or other factors
    employee_count = record.get("employee_count", "")

    if "1-10" in employee_count:
        return "< $1M"
    elif "11-50" in employee_count:
        return "$1M - $10M"
    elif "51-200" in employee_count:
        return "$10M - $50M"
    elif "201-500" in employee_count:
        return "$50M - $100M"
    elif "501-1000" in employee_count:
        return "$100M - $500M"
    elif "1001-5000" in employee_count:
        return "$500M - $1B"
    elif "5001-10000" in employee_count:
        return "$1B - $5B"
    elif "10000+" in employee_count:
        return "$5B+"
    else:
        return "Unknown"


def query_opensearch(max_companies: int = 10000) -> List[Dict[str, Any]]:
    """
    Query OpenSearch for random company data.

    Args:
        max_companies: Maximum number of companies to retrieve

    Returns:
        List of raw company records from OpenSearch
    """
    print(f"Querying OpenSearch for {max_companies} random companies...")

    seed = random.randint(0, 2**31 - 1)
    query = {
        "size": max_companies,
        "_source": True,
        "query": {
            "function_score": {
                "random_score": {"seed": seed}
            }
        }
    }

    resp = client.search(index=INDEX, body=query, request_timeout=60)

    companies = []
    for hit in resp["hits"]["hits"]:
        companies.append(hit["_source"])

    print(f"Retrieved {len(companies)} companies from OpenSearch")
    return companies


def save_jsonl_file(companies: List[Dict[str, Any]], filename: str):
    """Save companies as JSONL file (one JSON object per line)."""
    print(f"Saving {len(companies)} companies to {filename}...")

    with open(filename, "w", encoding='utf-8') as f:
        for company in companies:
            json.dump(company, f, ensure_ascii=False)
            f.write("\n")

    print(f"✅ Saved JSONL file: {filename}")


def save_transformed_json(companies: List[Dict[str, Any]], filename: str, title_suffix: str):
    """Save companies as transformed JSON file for Django."""
    print(f"Transforming and saving {len(companies)} companies to {filename}...")

    # Transform each company record
    transformed_companies = []
    for i, company in enumerate(companies, 1):
        try:
            transformed = extract_company_info(company)
            transformed_companies.append(transformed)

            if i % 500 == 0:
                print(f"  Transformed {i} companies...")

        except Exception as e:
            print(f"  Error transforming company {i}: {e}")
            continue

    # Create the final JSON structure
    output_data = {
        "type": "companies",
        "title": f"Real Company Data from AgSearch ({len(transformed_companies)} companies){title_suffix}",
        "description": f"Real company data extracted from AgSearch database via OpenSearch. Contains {len(transformed_companies)} companies with detailed information including industry, location, employee count, and descriptions.",
        "data": transformed_companies
    }

    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)

    print(f"✅ Saved transformed JSON file: {filename}")
    return len(transformed_companies)


def main():
    """Main function to create company datasets."""
    print("🚀 Starting OpenSearch to JSON conversion with transformation...")
    print("=" * 60)

    try:
        # Query OpenSearch for 10K companies
        print("\n📊 Step 1: Querying OpenSearch for 10,000 companies...")
        all_companies = query_opensearch(max_companies=10000)

        if not all_companies:
            print("❌ No companies retrieved from OpenSearch!")
            return

        print(f"✅ Successfully retrieved {len(all_companies)} companies")

        # Create 10K dataset
        print("\n📁 Step 2: Creating 10K company files...")

        # Save raw JSONL file (10K)
        save_jsonl_file(all_companies, "agsearch_random_10k.json")

        # Save transformed JSON file (10K)
        count_10k = save_transformed_json(
            all_companies,
            "agsearch_companies_10k.json",
            " - Full Dataset"
        )

        # Create 1K dataset (subset of 10K)
        print("\n📁 Step 3: Creating 1K company files...")
        companies_1k = all_companies[:1000]

        # Save raw JSONL file (1K)
        save_jsonl_file(companies_1k, "agsearch_random_1k.json")

        # Save transformed JSON file (1K)
        count_1k = save_transformed_json(
            companies_1k,
            "agsearch_companies_1k.json",
            " - Sample Dataset"
        )

        # Summary
        print("\n" + "=" * 60)
        print("🎉 CONVERSION COMPLETE!")
        print("=" * 60)
        print(f"📊 Total companies retrieved: {len(all_companies)}")
        print(f"📁 Files created:")
        print(f"   • agsearch_random_10k.json      - {len(all_companies)} companies (JSONL format)")
        print(f"   • agsearch_companies_10k.json   - {count_10k} companies (Django JSON format)")
        print(f"   • agsearch_random_1k.json       - {len(companies_1k)} companies (JSONL format)")
        print(f"   • agsearch_companies_1k.json    - {count_1k} companies (Django JSON format)")
        print("\n💡 Use the Django JSON files in your web application!")
        print("   - agsearch_companies_1k.json for faster loading")
        print("   - agsearch_companies_10k.json for full dataset")

    except Exception as e:
        print(f"❌ Error during conversion: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
