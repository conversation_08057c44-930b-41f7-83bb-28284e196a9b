# Context Loading for CETO Chat Terminal

This directory contains example context files that can be loaded into the CETO chat terminal to provide the LLM with background information at the start of a conversation.

## How to Use Context Loading

### Basic Usage

1. Start the chat terminal:
   ```bash
   python gaia/gaia_ceto/ceto_v002/chat_term.py
   ```

2. Load context data:
   ```
   /load_context companies_example.json
   ```

3. Start chatting - the LLM will now have access to the loaded context data!

### Command Syntax

```
/load_context <filename>
```

- `<filename>`: Name of the JSON context file (with or without .json extension)
- The file must be located in the `gaia/gaia_ceto/proto_mcp/example_contexts/` directory

### Behavior

- **No Active Conversation**: Creates a new conversation with a title based on the context data
- **Existing Conversation**: Adds the context to the current conversation
- **Context Format**: The context is added as a system message that informs the LLM about the available data

## Available Context Files

### companies_example.json
Contains information about various companies including:
- Company names and industries
- Founding dates and employee counts
- Revenue information
- Company descriptions

### products_example.json
Contains a product catalog with:
- Product IDs, names, and categories
- Pricing information
- Feature lists
- Product descriptions

### team_members_example.json
Contains team directory information with:
- Employee IDs, names, and roles
- Department assignments
- Contact information
- Skills and locations

## Creating Custom Context Files

Context files should follow this JSON structure:

```json
{
  "type": "your_context_type",
  "title": "Human-readable title",
  "description": "Description of what this context contains",
  "data": [
    {
      "id": "unique_identifier",
      "name": "Item name",
      "other_field": "other_value"
    }
  ]
}
```

### Required Fields
- `type`: A string identifying the type of context
- `title`: A human-readable title for the context
- `description`: A description of what the context contains

### Optional Fields
- `data`: An array of objects containing the actual context data
- Any other fields you want to include

### Data Format
The `data` field can contain:
- **Array of objects**: Most common format for structured data
- **Single object**: For configuration or settings data
- **Any JSON-serializable data**: The formatter will handle different data types

## Examples

### Loading Company Information
```
/load_context companies_example
```
This loads company data and allows you to ask questions like:
- "Tell me about Acme Corporation"
- "Which companies are in the technology industry?"
- "What's the revenue of TechNova?"

### Loading Product Catalog
```
/load_context products_example.json
```
This loads product data and allows you to ask questions like:
- "What electronics products do we have?"
- "Show me products under $20"
- "What are the features of the SmartWatch Pro?"

### Loading Team Directory
```
/load_context team_members_example
```
This loads team member data and allows you to ask questions like:
- "Who works in the Engineering department?"
- "What skills does Alice Johnson have?"
- "Who is located in San Francisco?"

## Tips

1. **File Extensions**: You can specify filenames with or without the `.json` extension
2. **Error Handling**: If a file doesn't exist, the command will show available files
3. **Multiple Contexts**: You can load multiple context files into the same conversation
4. **Conversation Titles**: When creating a new conversation, the title comes from the context's `title` field

## Technical Details

- Context files are loaded from: `gaia/gaia_ceto/proto_mcp/example_contexts/`
- Context is added as system messages to maintain conversation history
- The LLM receives formatted context that includes metadata and structured data
- Context loading works with all LLM providers (Mock, OpenAI, Anthropic, MCP)
