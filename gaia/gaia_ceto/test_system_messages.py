#!/usr/bin/env python3
"""
Test script to verify that system messages are properly handled in MCP client libraries.

This script tests both SSE and HTTP MCP client libraries to ensure they correctly
extract system messages from conversation history and pass them to the Anthropic API.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gaia.gaia_ceto.proto_mcp.mcp_sse_clientlib import MCPC<PERSON><PERSON>ib as MCPSSEClientLib
from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MC<PERSON><PERSON><PERSON><PERSON> as MCPHTTP<PERSON>lient<PERSON>ib


async def test_system_messages():
    """Test system message handling in both SSE and HTTP MCP clients."""
    
    print("Testing system message handling in MCP client libraries...")
    
    # Test data with system messages
    conversation_history = [
        {
            "role": "system",
            "content": "You are a helpful assistant that specializes in company information. You have access to a database of companies."
        },
        {
            "role": "system", 
            "content": "When asked about companies, provide detailed information including industry, founding year, and key details."
        },
        {
            "role": "user",
            "content": "Hello, can you help me with company information?"
        },
        {
            "role": "assistant",
            "content": "Hello! I'd be happy to help you with company information. What would you like to know?"
        }
    ]
    
    # Test query
    test_query = "Tell me about technology companies"
    
    # Test SSE client
    print("\n=== Testing SSE Client ===")
    try:
        sse_client = MCPSSEClientLib(debug_callback=debug_callback)
        
        # Connect to SSE server
        sse_success = await sse_client.connect_to_server("http://0.0.0.0:9000/sse")
        if sse_success:
            print("✓ Connected to SSE server")
            
            # Test with system messages
            result = await sse_client.process_query(
                query=test_query,
                conversation_history=conversation_history,
                max_tokens=100  # Small limit for testing
            )
            
            if result["error"]:
                print(f"✗ SSE test failed: {result['error']}")
            else:
                print(f"✓ SSE test successful: {result['final_text'][:100]}...")
                
        else:
            print("✗ Failed to connect to SSE server")
            
        await sse_client.cleanup()
        
    except Exception as e:
        print(f"✗ SSE test error: {e}")
    
    # Test HTTP client
    print("\n=== Testing HTTP Client ===")
    try:
        http_client = MCPHTTPClientLib(debug_callback=debug_callback)
        
        # Connect to HTTP server
        http_success = await http_client.connect_to_server("http://0.0.0.0:9000/mcp")
        if http_success:
            print("✓ Connected to HTTP server")
            
            # Test with system messages
            result = await http_client.process_query(
                query=test_query,
                conversation_history=conversation_history,
                max_tokens=100  # Small limit for testing
            )
            
            if result["error"]:
                print(f"✗ HTTP test failed: {result['error']}")
            else:
                print(f"✓ HTTP test successful: {result['final_text'][:100]}...")
                
        else:
            print("✗ Failed to connect to HTTP server")
            
        await http_client.cleanup()
        
    except Exception as e:
        print(f"✗ HTTP test error: {e}")
    
    print("\n=== Test Complete ===")


def debug_callback(level: str, message: str, data=None):
    """Debug callback for MCP clients."""
    if level in ["info", "error"]:
        print(f"[{level.upper()}] {message}")


if __name__ == "__main__":
    print("System Message Test for MCP Client Libraries")
    print("=" * 50)
    
    # Check if servers are running
    print("Note: This test requires MCP servers to be running:")
    print("  - SSE server at http://0.0.0.0:9000/sse")
    print("  - HTTP server at http://0.0.0.0:9000/mcp")
    print()
    
    try:
        asyncio.run(test_system_messages())
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"\nTest failed with error: {e}")
