# MCP Server Management in CETO Chat Terminal

This document describes the new third-party MCP server support added to the CETO chat terminal.

## Overview

The chat terminal now supports:
- **Server Registry**: Configuration-based management of multiple MCP servers
- **Unified Provider**: Single interface to work with multiple servers
- **Runtime Management**: Connect, disconnect, and switch between servers during chat sessions
- **Third-Party Support**: Easy integration of external MCP servers
- **Server Discovery**: Automatic tool discovery and health checking

## Architecture

### Components

1. **MCPServerRegistry** (`mcp_server_registry.py`)
   - Manages server configurations from JSON files
   - Supports built-in, third-party, and custom servers
   - Handles server health checks and tool discovery

2. **UnifiedMCPProvider** (`mcp_provider.py`)
   - Provides a single interface to multiple MCP servers
   - Manages connections and server switching
   - Compatible with existing chatobj interface

3. **Enhanced ChatTerminal** (`chat_term.py`)
   - New `mcp-unified` provider option
   - Server management commands
   - Configuration file support

### Configuration File

The server registry uses `mcp_servers.json` to define available servers:

```json
{
  "version": "1.0",
  "default_server": "gaia_local_sse",
  "servers": {
    "gaia_local_sse": {
      "name": "Gaia Local SSE Server",
      "protocol": "sse",
      "url": "http://0.0.0.0:9000/sse",
      "type": "builtin"
    }
  },
  "third_party_examples": {
    "filesystem": {
      "name": "Filesystem MCP Server",
      "protocol": "stdio",
      "command": "npx",
      "args": ["@modelcontextprotocol/server-filesystem", "/path/to/directory"],
      "type": "third_party"
    }
  }
}
```

## Usage

### Starting with Unified Provider

```bash
# Use the unified MCP provider
python chat_term.py --llm mcp-unified

# Use custom server configuration
python chat_term.py --llm mcp-unified --server-config /path/to/servers.json

# List available servers
python chat_term.py --list-servers
```

### Terminal Commands

Once in the chat terminal, use these new commands:

#### Server Management
- `/servers` - List all available MCP servers
- `/connect <server_id>` - Connect to a specific server
- `/disconnect <server_id>` - Disconnect from a server
- `/switch <server_id>` - Switch to a different server
- `/server_info` - Show current server information

#### Tool Management
- `/tools` - Show tools from active server
- `/tools <server_id>` - Show tools from specific server

#### Server Configuration
- `/add_server <id> <url> <protocol>` - Add a new server at runtime

### Example Session

```
$ python chat_term.py --llm mcp-unified

[No active conversation] > /servers

Available MCP servers (4):
------------------------------------------------------------

Built-in servers:
  ✗ Gaia Local SSE Server
    Protocol: sse
    URL: http://0.0.0.0:9000/sse
    Description: Local Gaia MCP server using SSE protocol
    Tools: echostring, echostring_table, get_company_categories_matching...

[No active conversation] > /connect gaia_local_sse
Successfully connected to server: gaia_local_sse
Available tools: echostring, echostring_table, get_llm_completion

[No active conversation] > /new
Created new conversation: Conversation 2024-01-15 10:30:00

[Conversation 2024-01-15 10:30:00] > Hello, can you echo "test message"?
Assistant: I'll echo that message for you using the echostring tool.

[Calling tool 'echostring'...]
Result: test message

The message "test message" has been echoed successfully.
```

## Adding Third-Party Servers

### Method 1: Configuration File

Add servers to `mcp_servers.json`:

```json
{
  "servers": {
    "my_custom_server": {
      "name": "My Custom Server",
      "description": "Custom MCP server for specific tasks",
      "protocol": "http",
      "url": "http://localhost:8080/mcp",
      "type": "custom",
      "tools": ["custom_tool1", "custom_tool2"]
    }
  }
}
```

### Method 2: Runtime Addition

Use the `/add_server` command:

```
/add_server my_server http://localhost:8080/mcp http
/connect my_server
```

### Method 3: Third-Party Examples

The configuration includes examples for popular MCP servers:

- **Filesystem Server**: File operations
- **GitHub Server**: GitHub API integration
- **Brave Search**: Web search capabilities
- **PostgreSQL Server**: Database operations
- **Anthropic Computer Use**: Computer interaction

To use these, install the required packages and update the configuration with your API keys.

## Supported Protocols

1. **HTTP/SSE**: Web-based servers (built-in support)
2. **STDIO**: Command-line servers (planned)
3. **WebSocket**: Real-time servers (planned)

## Server Types

- **builtin**: Servers developed as part of GAIA
- **third_party**: External MCP servers from the community
- **custom**: User-defined servers

## Health Checking

The system automatically checks server health:
- HTTP/SSE servers: GET request to health endpoint
- Connection status tracking
- Automatic reconnection (planned)

## Tool Discovery

- Automatic tool enumeration on connection
- Tool metadata caching
- Dynamic tool availability updates

## Error Handling

- Graceful fallback to MockLLM on connection failures
- Detailed error messages and logging
- Connection retry mechanisms (planned)

## Development

### Testing

Run the test script to verify functionality:

```bash
python test_mcp_servers.py
```

### Adding New Server Types

1. Extend `MCPServerInfo` with new protocol support
2. Update `UnifiedMCPProvider._create_client()` method
3. Add protocol-specific client implementation
4. Update configuration schema

### Custom Client Libraries

To add support for new MCP client libraries:

1. Implement the same interface as existing clients
2. Add import handling in `mcp_provider.py`
3. Update protocol detection logic

## Troubleshooting

### Common Issues

1. **Server not connecting**: Check URL and ensure server is running
2. **Tools not available**: Verify server supports tool listing
3. **Configuration errors**: Validate JSON syntax and required fields

### Debug Mode

Enable debug logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Log Files

Check logs for connection and tool call details:
- Server connection attempts
- Tool discovery results
- Error messages and stack traces

## Future Enhancements

- **Server Discovery**: Automatic detection of local MCP servers
- **Load Balancing**: Distribute requests across multiple servers
- **Caching**: Tool result caching and server metadata
- **Monitoring**: Server performance and availability metrics
- **GUI**: Graphical server management interface
