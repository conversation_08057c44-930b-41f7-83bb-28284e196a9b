#!/usr/bin/env python3
"""
Test script for MCP server registry and unified provider functionality.

This script demonstrates how to use the new MCP server management features
in chat_term.py without requiring a full terminal session.
"""

import asyncio
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mcp_server_registry import MCPServerRegistry, MCPServerInfo
from mcp_provider import UnifiedMCPProvider


async def test_server_registry():
    """Test the server registry functionality."""
    print("=== Testing MCP Server Registry ===")
    
    # Create a registry (will load from default config)
    registry = MCPServerRegistry()
    
    # List all servers
    servers = registry.list_servers()
    print(f"\nFound {len(servers)} servers:")
    
    for i, server in enumerate(servers, 1):
        print(f"{i}. {server.name}")
        print(f"   Protocol: {server.protocol}")
        print(f"   URL: {server.url or 'N/A'}")
        print(f"   Type: {server.type}")
        print(f"   Description: {server.description}")
        if server.tools:
            print(f"   Tools: {', '.join(server.tools[:3])}{'...' if len(server.tools) > 3 else ''}")
        print()
    
    # Test getting default server
    default_server = registry.get_default_server()
    if default_server:
        print(f"Default server: {default_server.name}")
    
    # Test adding a custom server
    custom_server = MCPServerInfo(
        name="Test Custom Server",
        description="A test server added programmatically",
        protocol="http",
        url="http://localhost:9999/mcp",
        type="custom"
    )
    
    registry.add_server("test_custom", custom_server)
    print(f"Added custom server. Total servers: {len(registry.list_servers())}")
    
    return registry


async def test_unified_provider(registry):
    """Test the unified MCP provider."""
    print("\n=== Testing Unified MCP Provider ===")
    
    # Create a unified provider
    provider = UnifiedMCPProvider(registry)
    
    # List available servers
    available_servers = list(provider.registry.servers.keys())
    print(f"Available servers: {', '.join(available_servers)}")
    
    # Try to connect to a server (this will likely fail since servers aren't running)
    if available_servers:
        test_server = available_servers[0]
        print(f"\nTrying to connect to: {test_server}")
        
        try:
            success = await provider.connect_to_server(test_server)
            if success:
                print(f"✓ Successfully connected to {test_server}")
                
                # List tools
                tools = provider.get_available_tools()
                if tools:
                    print(f"Available tools: {', '.join(tools)}")
                else:
                    print("No tools available")
            else:
                print(f"✗ Failed to connect to {test_server}")
        except Exception as e:
            print(f"✗ Error connecting to {test_server}: {e}")
    
    # Clean up
    await provider.cleanup()


def test_config_file():
    """Test loading the configuration file."""
    print("\n=== Testing Configuration File ===")
    
    config_path = "mcp_servers.json"
    if os.path.exists(config_path):
        print(f"✓ Configuration file exists: {config_path}")
        
        # Try to load it
        try:
            registry = MCPServerRegistry(config_path)
            servers = registry.list_servers()
            print(f"✓ Successfully loaded {len(servers)} servers from config")
            
            # Show server types
            builtin_count = len([s for s in servers if s.type == "builtin"])
            third_party_count = len([s for s in servers if s.type == "third_party"])
            custom_count = len([s for s in servers if s.type == "custom"])
            
            print(f"  - Built-in servers: {builtin_count}")
            print(f"  - Third-party servers: {third_party_count}")
            print(f"  - Custom servers: {custom_count}")
            
        except Exception as e:
            print(f"✗ Error loading config: {e}")
    else:
        print(f"✗ Configuration file not found: {config_path}")


async def main():
    """Main test function."""
    print("MCP Server Management Test")
    print("=" * 50)
    
    # Test configuration file
    test_config_file()
    
    # Test server registry
    registry = await test_server_registry()
    
    # Test unified provider
    await test_unified_provider(registry)
    
    print("\n=== Test Complete ===")
    print("\nTo use the new functionality in chat_term.py:")
    print("1. Start with unified MCP provider:")
    print("   python chat_term.py --llm mcp-unified")
    print("\n2. Use new commands:")
    print("   /servers          - List available servers")
    print("   /connect <id>     - Connect to a server")
    print("   /tools            - Show available tools")
    print("   /server_info      - Show current server info")
    print("   /switch <id>      - Switch to different server")
    print("\n3. List servers from command line:")
    print("   python chat_term.py --list-servers")


if __name__ == "__main__":
    asyncio.run(main())
