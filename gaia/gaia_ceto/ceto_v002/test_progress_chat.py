#!/usr/bin/env python3
"""
Test script to verify that chat_term.py shows incremental progress for direct tool calls.

This script tests the enhanced chat_term.py functionality that detects direct tool calls
and displays progress updates in real-time using FastMCP.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from gaia.gaia_ceto.ceto_v002.chat_term import ChatTerminal


async def test_progress_display():
    """Test the progress display functionality."""
    
    print("Testing Progress Display in Chat Terminal")
    print("=" * 50)
    
    # Test with MCP HTTP provider
    print("\n=== Testing with MCP HTTP Provider ===")
    try:
        terminal = ChatTerminal(
            storage_dir="./test_conversations",
            user_id="test_user",
            llm_provider="mcp-http",
            model_name="http://localhost:9000/mcp"
        )
        
        print("✓ ChatTerminal initialized with MCP HTTP provider")
        
        # Test direct tool call detection
        test_messages = [
            "long_task",
            "long_task()",
            "echostring_longrunning",
            "hello world",  # Should not be detected as direct tool call
            "Please use the long_task tool"  # Should not be detected as direct tool call
        ]
        
        for message in test_messages:
            is_direct, tool_name = terminal._is_direct_tool_call(message)
            status = "✓ DIRECT" if is_direct else "✗ REGULAR"
            print(f"{status}: '{message}' -> tool: {tool_name}")
        
        print("\n📊 Direct tool call detection working correctly")
        
    except Exception as e:
        print(f"✗ Error testing MCP HTTP: {e}")
    
    # Test with MCP SSE provider
    print("\n=== Testing with MCP SSE Provider ===")
    try:
        terminal = ChatTerminal(
            storage_dir="./test_conversations",
            user_id="test_user",
            llm_provider="mcp",
            model_name="http://localhost:9000/sse"
        )
        
        print("✓ ChatTerminal initialized with MCP SSE provider")
        
    except Exception as e:
        print(f"✗ Error testing MCP SSE: {e}")
    
    print("\n=== Test Complete ===")
    print("To test live progress display:")
    print("1. Start an MCP server with long_task tool:")
    print("   cd gaia/gaia_ceto/proto_mcp/long_task_test")
    print("   python minimal_mcp_server.py")
    print()
    print("2. Run chat_term.py with MCP provider:")
    print("   python chat_term.py --llm mcp-http")
    print()
    print("3. Create a new conversation and type: long_task")
    print("   You should see progress bars like:")
    print("   📊 [████████░░░░░░░░░░░░░░░░░░░░]  20.0% 1/5 – Custom message")


if __name__ == "__main__":
    print("Progress Display Test for Chat Terminal")
    print("=" * 50)
    
    try:
        asyncio.run(test_progress_display())
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"\nTest failed with error: {e}")
