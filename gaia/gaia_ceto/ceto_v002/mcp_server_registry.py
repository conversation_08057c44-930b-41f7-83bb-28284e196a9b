#!/usr/bin/env python3
"""
MCP Server Registry

This module provides functionality for managing and discovering MCP servers,
including built-in servers, third-party servers, and runtime server registration.
"""

import json
import logging
import asyncio
import aiohttp
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


@dataclass
class MCPServerInfo:
    """Information about an MCP server."""
    name: str
    description: str
    protocol: str  # "sse", "http", "stdio"
    url: Optional[str] = None
    command: Optional[str] = None
    args: Optional[List[str]] = None
    type: str = "builtin"  # "builtin", "third_party", "custom"
    health_check_path: Optional[str] = None
    tools: Optional[List[str]] = None
    model: str = "claude-3-5-sonnet-20240620"
    environment: Optional[Dict[str, str]] = None
    documentation: Optional[str] = None
    last_health_check: Optional[datetime] = None
    is_healthy: bool = False
    discovered_tools: Optional[List[str]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, handling datetime serialization."""
        data = asdict(self)
        if self.last_health_check:
            data['last_health_check'] = self.last_health_check.isoformat()
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPServerInfo':
        """Create from dictionary, handling datetime deserialization."""
        if 'last_health_check' in data and data['last_health_check']:
            data['last_health_check'] = datetime.fromisoformat(data['last_health_check'])
        return cls(**data)


class MCPServerRegistry:
    """Registry for managing MCP servers."""

    def __init__(self, config_path: Optional[str] = None):
        """Initialize the server registry.
        
        Args:
            config_path: Path to the server configuration file.
                        If None, uses the default config in the same directory.
        """
        self.config_path = config_path or self._get_default_config_path()
        self.servers: Dict[str, MCPServerInfo] = {}
        self.default_server: Optional[str] = None
        self._load_config()

    def _get_default_config_path(self) -> str:
        """Get the default configuration file path."""
        current_dir = Path(__file__).parent
        return str(current_dir / "mcp_servers.json")

    def _load_config(self):
        """Load server configuration from file."""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)
            
            self.default_server = config.get('default_server')
            
            # Load built-in servers
            for server_id, server_data in config.get('servers', {}).items():
                self.servers[server_id] = MCPServerInfo.from_dict(server_data)
            
            # Load third-party examples (but mark them as disabled by default)
            for server_id, server_data in config.get('third_party_examples', {}).items():
                server_info = MCPServerInfo.from_dict(server_data)
                # Add a prefix to distinguish examples
                example_id = f"example_{server_id}"
                self.servers[example_id] = server_info
            
            logger.info(f"Loaded {len(self.servers)} servers from {self.config_path}")
            
        except FileNotFoundError:
            logger.warning(f"Config file not found: {self.config_path}")
            self._create_default_config()
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in config file: {e}")
            raise
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            raise

    def _create_default_config(self):
        """Create a minimal default configuration."""
        default_config = {
            "version": "1.0",
            "description": "MCP Server Registry for CETO Chat Terminal",
            "default_server": "gaia_local_sse",
            "servers": {
                "gaia_local_sse": {
                    "name": "Gaia Local SSE Server",
                    "description": "Local Gaia MCP server using SSE protocol",
                    "protocol": "sse",
                    "url": "http://0.0.0.0:9000/sse",
                    "type": "builtin",
                    "model": "claude-3-5-sonnet-20240620"
                }
            }
        }
        
        with open(self.config_path, 'w') as f:
            json.dump(default_config, f, indent=2)
        
        self.default_server = default_config['default_server']
        for server_id, server_data in default_config['servers'].items():
            self.servers[server_id] = MCPServerInfo.from_dict(server_data)

    def list_servers(self, server_type: Optional[str] = None) -> List[MCPServerInfo]:
        """List available servers.
        
        Args:
            server_type: Filter by server type ("builtin", "third_party", "custom")
        
        Returns:
            List of server information objects.
        """
        servers = list(self.servers.values())
        if server_type:
            servers = [s for s in servers if s.type == server_type]
        return servers

    def get_server(self, server_id: str) -> Optional[MCPServerInfo]:
        """Get server information by ID.
        
        Args:
            server_id: The server identifier.
        
        Returns:
            Server information or None if not found.
        """
        return self.servers.get(server_id)

    def get_default_server(self) -> Optional[MCPServerInfo]:
        """Get the default server."""
        if self.default_server:
            return self.get_server(self.default_server)
        return None

    def add_server(self, server_id: str, server_info: MCPServerInfo):
        """Add a new server to the registry.
        
        Args:
            server_id: Unique identifier for the server.
            server_info: Server information object.
        """
        self.servers[server_id] = server_info
        logger.info(f"Added server: {server_id}")

    def remove_server(self, server_id: str) -> bool:
        """Remove a server from the registry.
        
        Args:
            server_id: The server identifier.
        
        Returns:
            True if server was removed, False if not found.
        """
        if server_id in self.servers:
            del self.servers[server_id]
            logger.info(f"Removed server: {server_id}")
            return True
        return False

    async def health_check(self, server_id: str) -> bool:
        """Perform health check on a server.
        
        Args:
            server_id: The server identifier.
        
        Returns:
            True if server is healthy, False otherwise.
        """
        server = self.get_server(server_id)
        if not server:
            return False

        # Only check HTTP/SSE servers for now
        if server.protocol not in ["http", "sse"] or not server.url:
            return False

        try:
            health_url = server.url
            if server.health_check_path:
                health_url = server.url.rstrip('/') + server.health_check_path

            async with aiohttp.ClientSession() as session:
                async with session.get(health_url, timeout=5) as response:
                    is_healthy = response.status == 200
                    server.is_healthy = is_healthy
                    server.last_health_check = datetime.now()
                    return is_healthy

        except Exception as e:
            logger.debug(f"Health check failed for {server_id}: {e}")
            server.is_healthy = False
            server.last_health_check = datetime.now()
            return False

    async def discover_tools(self, server_id: str) -> Optional[List[str]]:
        """Discover available tools from a server.
        
        Args:
            server_id: The server identifier.
        
        Returns:
            List of tool names or None if discovery failed.
        """
        # This would require connecting to the server and querying tools
        # For now, return the configured tools
        server = self.get_server(server_id)
        if server and server.tools:
            return server.tools
        return None

    def save_config(self):
        """Save the current registry to the configuration file."""
        config = {
            "version": "1.0",
            "description": "MCP Server Registry for CETO Chat Terminal",
            "default_server": self.default_server,
            "servers": {},
            "third_party_examples": {}
        }

        for server_id, server_info in self.servers.items():
            server_dict = server_info.to_dict()
            if server_id.startswith("example_"):
                # Remove the example_ prefix for storage
                actual_id = server_id[8:]
                config["third_party_examples"][actual_id] = server_dict
            else:
                config["servers"][server_id] = server_dict

        with open(self.config_path, 'w') as f:
            json.dump(config, f, indent=2)

        logger.info(f"Saved registry to {self.config_path}")

    def find_servers_with_tool(self, tool_name: str) -> List[str]:
        """Find servers that provide a specific tool.
        
        Args:
            tool_name: Name of the tool to search for.
        
        Returns:
            List of server IDs that provide the tool.
        """
        matching_servers = []
        for server_id, server_info in self.servers.items():
            if server_info.tools and tool_name in server_info.tools:
                matching_servers.append(server_id)
            elif server_info.discovered_tools and tool_name in server_info.discovered_tools:
                matching_servers.append(server_id)
        return matching_servers
